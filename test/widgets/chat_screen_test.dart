import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:provider/provider.dart';
import '../../lib/screens/chat_screen.dart';
import '../../lib/models/transaction_model.dart';
import '../../lib/models/parse_result.dart';
import '../../lib/widgets/quick_reply_widget.dart';
import '../../lib/services/parser/transaction_parsing_service.dart';
import '../helpers/test_helpers.dart';
import '../mocks/mock_storage_service.dart';

void main() {
  group('ChatScreen Soft Fail Tests', () {
    late TransactionProvider transactionProvider;
    late MockStorageService mockStorage;
    late ValueNotifier<TransactionParsingService?> parsingServiceNotifier;

    setUp(() async {
      mockStorage = MockStorageService();
      await mockStorage.init();
      transactionProvider = TransactionProvider(mockStorage);
      parsingServiceNotifier = ValueNotifier<TransactionParsingService?>(null);
    });

    Widget createTestWidget({Widget? child}) {
      return MultiProvider(
        providers: [
          ChangeNotifierProvider<TransactionProvider>.value(
            value: transactionProvider,
          ),
          ChangeNotifierProvider<ValueNotifier<TransactionParsingService?>>.value(
            value: parsingServiceNotifier,
          ),
        ],
        child: MaterialApp(
          home: child ?? ChatScreen(),
        ),
      );
    }

    testWidgets('should display chat screen', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.byType(ChatScreen), findsOneWidget);
      expect(find.byType(Scaffold), findsOneWidget);
    });

    testWidgets('should show message input field', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      expect(find.byType(TextField), findsOneWidget);
    });

    testWidgets('should handle message sending', (WidgetTester tester) async {
      await tester.pumpWidget(createTestWidget());
      
      // Find the text field and enter a message
      final textField = find.byType(TextField);
      await tester.enterText(textField, 'Test message');
      await tester.pump();
      
      // Find and tap the send button
      final sendButton = find.byIcon(Icons.send);
      if (sendButton.evaluate().isNotEmpty) {
        await tester.tap(sendButton);
        await tester.pump();
      }
      
      // Verify the message was processed
      expect(find.text('Test message'), findsOneWidget);
    });

    group('Soft Fail Flow Tests', () {
      testWidgets('should handle needsType parse result', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Create a transaction that needs type selection
        final partialTransaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsType(partialTransaction);
        
        // Simulate the soft fail scenario
        // This would typically be triggered by the parser
        expect(parseResult.needsTypeSelection, isTrue);
        expect(parseResult.status, equals(ParseStatus.needsType));
      });

      testWidgets('should handle needsCategory parse result', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Create a transaction that needs category selection
        final partialTransaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.needsCategory(partialTransaction);
        
        // Simulate the soft fail scenario
        expect(parseResult.needsCategorySelection, isTrue);
        expect(parseResult.status, equals(ParseStatus.needsCategory));
      });

      testWidgets('should display system messages with quick replies', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Create a system message with quick replies
        final message = ChatMessage.systemWithQuickReplies(
          id: 'test-quick-reply',
          text: 'Please select transaction type:',
          timestamp: DateTime.now(),
          quickReplies: ['Expense', 'Income', 'Cancel'],
          quickReplyId: 'type-selection',
        );
        
        // Add message to provider
        await transactionProvider.addMessage(message);
        await tester.pump();

        // Verify the message is displayed
        expect(find.text('Please select transaction type:'), findsOneWidget);

        // Verify quick reply buttons are shown
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Income'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should handle quick reply selection', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create a system message with quick replies
        final message = ChatMessage.systemWithQuickReplies(
          id: 'test-quick-reply',
          text: 'Please select transaction type:',
          timestamp: DateTime.now(),
          quickReplies: ['Expense', 'Income'],
          quickReplyId: 'type-selection',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();

        // Tap on a quick reply option
        await tester.tap(find.text('Expense'));
        await tester.pump();

        // Verify the selection was processed
        // This would typically update the pending transaction
        expect(find.text('Expense'), findsOneWidget);
      });

      testWidgets('should handle learning confirmation', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create a learning confirmation message
        final message = ChatMessage.systemWithQuickReplies(
          id: 'learning-confirmation',
          text: 'Should I remember this categorization for future transactions?',
          timestamp: DateTime.now(),
          quickReplies: ['Yes', 'No'],
          quickReplyId: 'learning-confirmation',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();
        
        // Verify the learning confirmation is displayed
        expect(find.text('Should I remember this categorization for future transactions?'), findsOneWidget);
        expect(find.text('Yes'), findsOneWidget);
        expect(find.text('No'), findsOneWidget);
      });

      testWidgets('should handle transaction completion', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Create a completed transaction
        final transaction = TestHelpers.createTestTransaction();
        
        // Add transaction to provider
        transactionProvider.addTransaction(transaction);
        await tester.pump();
        
        // Verify transaction was added
        expect(transactionProvider.transactions.length, equals(1));
        expect(transactionProvider.transactions.first.id, equals(transaction.id));
      });

      testWidgets('should handle error states gracefully', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create a failed parse result with a dummy transaction
        final dummyTransaction = TestHelpers.createTestTransaction();
        final parseResult = ParseResult.failed(dummyTransaction, 'Test error message');

        // Verify error handling
        expect(parseResult.status, equals(ParseStatus.failed));
        expect(parseResult.error, equals('Test error message'));
        expect(parseResult.isSuccess, isFalse);
      });

      testWidgets('should handle multiple quick reply interactions', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Add multiple messages with quick replies
        final typeMessage = ChatMessage.systemWithQuickReplies(
          id: 'type-selection',
          text: 'Select transaction type:',
          timestamp: DateTime.now(),
          quickReplies: ['Expense', 'Income'],
          quickReplyId: 'type-selection',
        );
        
        final categoryMessage = ChatMessage.systemWithQuickReplies(
          id: 'category-selection',
          text: 'Select category:',
          timestamp: DateTime.now().add(Duration(seconds: 1)),
          quickReplies: ['Food', 'Transport', 'Other'],
          quickReplyId: 'category-selection',
        );
        
        await transactionProvider.addMessage(typeMessage);
        await transactionProvider.addMessage(categoryMessage);
        await tester.pump();
        
        // Verify both messages are displayed
        expect(find.text('Select transaction type:'), findsOneWidget);
        expect(find.text('Select category:'), findsOneWidget);
        
        // Verify all quick reply options are available
        expect(find.text('Expense'), findsOneWidget);
        expect(find.text('Food'), findsOneWidget);
        expect(find.text('Transport'), findsOneWidget);
      });
    });

    group('Amount Confirmation UI Tests', () {
      testWidgets('should display amount confirmation message correctly', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create a ParseResult with amount confirmation needed
        final candidateAmounts = [69.0, 100.0];
        final candidateTexts = ['69', '100'];
        final transaction = TestHelpers.createTestTransaction(
          amount: 69.0, // Default to first candidate
          description: 'an com tai lux69 100',
        );

        final parseResult = ParseResult.needsAmountConfirmation(
          transaction,
          candidateAmounts,
          candidateTexts,
        );

        // Verify the parse result is correct
        expect(parseResult.status, equals(ParseStatus.needsAmountConfirmation));
        expect(parseResult.candidateAmounts, equals(candidateAmounts));
        expect(parseResult.candidateTexts, equals(candidateTexts));

        // Create amount confirmation message
        final message = ChatMessage.systemWithQuickReplies(
          id: 'amount-confirmation-test',
          text: 'I found multiple possible amounts in your message. Which amount did you mean?',
          timestamp: DateTime.now(),
          quickReplies: [...candidateTexts, 'Cancel'],
          quickReplyId: 'amount-confirmation',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();

        // Verify the message is displayed correctly
        expect(find.text('I found multiple possible amounts in your message. Which amount did you mean?'), findsOneWidget);

        // Verify quick reply buttons are displayed with correct formatting
        expect(find.text('69'), findsOneWidget);
        expect(find.text('100'), findsOneWidget);
        expect(find.text('Cancel'), findsOneWidget);

        // Verify QuickReplyWidget is present
        expect(find.byType(QuickReplyWidget), findsOneWidget);
      });

      testWidgets('should show candidate amounts in correct order', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Test with multiple amounts in specific order
        final candidateTexts = ['45', '200', '1.5k'];
        final message = ChatMessage.systemWithQuickReplies(
          id: 'order-test',
          text: 'Multiple amounts detected',
          timestamp: DateTime.now(),
          quickReplies: [...candidateTexts, 'Cancel'],
          quickReplyId: 'order-test',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();

        // Find all quick reply buttons
        final quickReplyWidget = find.byType(QuickReplyWidget);
        expect(quickReplyWidget, findsOneWidget);

        // Verify all candidate amounts are present
        for (final amount in candidateTexts) {
          expect(find.text(amount), findsOneWidget);
        }

        // Verify Cancel button is present
        expect(find.text('Cancel'), findsOneWidget);
      });

      testWidgets('should handle user selecting amount from candidates', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create amount confirmation message
        final message = ChatMessage.systemWithQuickReplies(
          id: 'selection-test',
          text: 'Which amount did you mean?',
          timestamp: DateTime.now(),
          quickReplies: ['69', '100', 'Cancel'],
          quickReplyId: 'selection-test',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();

        // Tap on the second amount option
        await tester.tap(find.text('100'));
        await tester.pump();

        // Verify the selection was processed (button should still be visible)
        expect(find.text('100'), findsOneWidget);

        // Verify no exceptions occurred
        expect(tester.takeException(), isNull);
      });

      testWidgets('should handle user cancelling amount selection', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create amount confirmation message
        final message = ChatMessage.systemWithQuickReplies(
          id: 'cancel-test',
          text: 'Which amount did you mean?',
          timestamp: DateTime.now(),
          quickReplies: ['50', '200', 'Cancel'],
          quickReplyId: 'cancel-test',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();

        // Tap on Cancel
        await tester.tap(find.text('Cancel'));
        await tester.pump();

        // Verify the cancel was processed
        expect(find.text('Cancel'), findsOneWidget);

        // Verify no exceptions occurred
        expect(tester.takeException(), isNull);
      });
    });

    group('Edge Cases', () {
      testWidgets('should handle empty message list', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());
        
        // Verify empty state
        expect(transactionProvider.messages.isEmpty, isTrue);
      });

      testWidgets('should handle malformed quick replies', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Create message with empty quick replies
        final message = ChatMessage.systemWithQuickReplies(
          id: 'empty-replies',
          text: 'Test message',
          timestamp: DateTime.now(),
          quickReplies: [],
          quickReplyId: 'empty-test',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();

        // Should handle gracefully
        expect(find.text('Test message'), findsOneWidget);
      });

      testWidgets('should handle rapid user interactions', (WidgetTester tester) async {
        await tester.pumpWidget(createTestWidget());

        // Add message with quick replies
        final message = ChatMessage.systemWithQuickReplies(
          id: 'rapid-test',
          text: 'Quick test',
          timestamp: DateTime.now(),
          quickReplies: ['Option 1', 'Option 2'],
          quickReplyId: 'rapid-test',
        );

        await transactionProvider.addMessage(message);
        await tester.pump();
        
        // Rapidly tap multiple options
        for (int i = 0; i < 3; i++) {
          if (find.text('Option 1').evaluate().isNotEmpty) {
            await tester.tap(find.text('Option 1'));
            await tester.pump();
          }
        }
        
        // Should handle without errors
        expect(tester.takeException(), isNull);
      });
    });
  });
}
